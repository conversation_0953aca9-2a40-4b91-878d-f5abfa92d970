<!-- content -->
<div id="content" class="app-content" role="main">

	<nav class="navbar navbar-controls bg-white">
		<div class="container-fluid">
			<!-- Brand and toggle get grouped for better mobile display -->
			<div class="navbar-header full">
				<ul class="nav navbar-nav nav-pills nav-list-button navbar-left">
					<li><button id="go-to-list" type="button" class="btn btn-default no-border navbar-btn navbar-left"><i class="fa fa-chevron-left"></i> Back</button></li>
					<li>
						<button type="button" data-toggle="dropdown" class="btn btn-default navbar-btn navbar-left">History <i class="caret"></i></button>
						<div class="dropdown-menu w-xxl">
							<div class="panel bg-white bg-inherit">
								<div class="panel-heading b-light bg-light">
								<strong>History</strong>
								</div>
								<div class="list-group list-group-alt" id="view-history">
									<?php
										$this->renderPartial('_view_cra_history',array('histories'=>$histories));
									?>
								</div>
							</div>
						</div>
					</li>

					<!-- Marketing Groups Buttons -->
					<?php if(in_array(Yii::app()->user->groupName, array("Marketing", "CPD", "ACD", "PPD", "LUXE"))): ?>
						<?php if($item->status == 'WAIT_FOR_MKT_SUBMISSION' || $item->status == 'WAIT_FOR_STRAD_DIRECTOR_TO_ASSIGN'): ?>
						<li><button type="button" class="submit btn btn-default navbar-btn navbar-left" id="delete">Delete</button></li>
						<?php endif; ?>

							<?php if($item->status == 'WAIT_FOR_MKT_SUBMISSION' || $item->status == 'WAIT_FOR_STRAD_DIRECTOR_TO_ASSIGN' || $item->status == 'WAIT_FOR_STRAD_RISK_ASSESSMENT' || $item->status == 'REJECTED' ||  $item->status == 'RECALLED'): ?>
						<li><button type="button" class="submit btn btn-default navbar-btn navbar-left" id="save">Save</button></li>
						<?php endif; ?>

						<?php if($item->status == 'WAIT_FOR_MKT_SUBMISSION'): ?>
							<li><button type="button" class="submit btn btn-default navbar-btn navbar-left" id="save-submit">Save and submit</button></li>
						<?php elseif($item->status == 'REJECTED' ||  $item->status == 'RECALLED'): ?>
							<li><button type="button" class="submit btn btn-default navbar-btn navbar-left" id="save-revise">Save and revise</button></li>
						<?php endif; ?>

						<!-- Recall functionality for STRAD stages -->
						<?php if(!isset($item->deleted)): ?>
						<?php if($item->status == "WAIT_FOR_STRAD_DIRECTOR_TO_ASSIGN" || $item->status == "WAIT_FOR_STRAD_RISK_ASSESSMENT"): ?>
						<li>
							<button type="button" data-toggle="dropdown" class="btn btn-default navbar-btn navbar-left">Recall <i class="caret"></i></button>
							<div class="dropdown-menu keep-dropdown w-xxl">
								<div class="panel bg-white bg-inherit">
									<div class="panel-heading b-light bg-light">
										<strong>Reason to recall</strong>
									</div>
									<div class="panel-body">
										<textarea class="form-control" name="reason-to-recall" id="reason-to-recall" rows="3"></textarea>
									</div>
									<div class="panel-footer text-right">
										<button class="btn btn-primary" id="recall">Recall</button>
									</div>
								</div>
							</div>
						</li>
						<?php endif; ?>
						<?php endif; ?>
					<?php endif; ?>
				</ul>
			</div>
		</div><!-- /.container-fluid -->
	</nav>

	<div class="p-md">
		<form id="cra-form" role="form" class="form-horizontal ng-pristine ng-valid" method="post" enctype="multipart/form-data" action="<?php echo Yii::app()->createUrl("cra/write");?>">
		<input type="hidden" name="typeAction" id="typeAction">
		<input type="hidden" name="id" id="id" value="<?php echo $item->id;?>">
			<h2>PROCESSING INFORMATION<?php if(isset($item->deleted)) echo ' (CRA IS DELETED AT '.date("H:i d-m-Y",$item->deleted/1000).')'?></h2>
			<?php
			$this->renderPartial('_process_view',array('item'=>$item,'requests'=>$requests));
			?>

			<h2>CLAIM RISK ASSESSMENT</h2>
			<?php
			$this->renderPartial('_claims_section',array(
				'item'=>$item,
				'claims'=>$claims,
				'robustnessOptions'=>$robustnessOptions,
				'finePenaltyOptions'=>$finePenaltyOptions,
				'showClaimForm'=>false // Marketing groups don't need the claim form
			));
			?>

			<h2>CRA DETAILS</h2>
			<?php
			$this->renderPartial('_form',array('item'=>$item,'requests'=>$requests,'typeForm'=>'update'));
			?>
		
		</form>
	</div>
</div>
<!-- / content -->

<!-- Note: Robustness and Fine/Penalty modals are now dynamically created by cra-claims.js -->

<script>
// Wait for jQuery to be loaded before executing
function initializeCraUpdatePage() {
	// Check if jQuery is available
	if (typeof $ === 'undefined') {
		// jQuery not loaded yet, wait and try again
		setTimeout(initializeCraUpdatePage, 100);
		return;
	}

	$(document).ready(function() {
		console.log('CRA Update page JavaScript initialized');

		// Navigation
		$('#go-to-list').click(function() {
			window.location.href = baseUrl + '/cra/index';
		});

		// Note: Robustness and fine-penalty link handlers are now handled by cra-claims.js
		// which provides better error handling and uses cached dropdown data
	});
}

// Start initialization
initializeCraUpdatePage();
</script>
